package com.snakeclash2d.snakeclash.game;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;

/**
 * Represents an obstacle (wall) in the game.
 */
public class Obstacle {
    private int x, y;
    private ObstacleType type;

    public Obstacle(int x, int y, ObstacleType type) {
        this.x = x;
        this.y = y;
        this.type = type;
    }

    public void render(Canvas canvas, Paint paint, float cellSize, float offsetX, float offsetY) {
        float left = offsetX + x * cellSize;
        float top = offsetY + y * cellSize;
        float right = left + cellSize;
        float bottom = top + cellSize;

        // Use enhanced obstacle rendering
        GraphicsUtils.drawEnhancedObstacle(canvas, paint, left, top, right, bottom, type);
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public ObstacleType getType() {
        return type;
    }
}
