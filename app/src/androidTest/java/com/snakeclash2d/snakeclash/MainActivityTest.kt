package com.snakeclash2d.snakeclash

import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Instrumented test for MainActivity.
 * Tests the activity lifecycle and basic functionality.
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class MainActivityTest {
    
    @get:Rule
    val activityRule = ActivityScenarioRule(SnakeActivity::class.java)
    
    @Test
    fun testActivityLaunch() {
        // Simply launching the activity without crashing is a success
        activityRule.scenario.onActivity { activity ->
            // Activity is created and running
            assert(activity.isDestroyed.not())
        }
    }
    
    @Test
    fun testActivityLifecycle() {
        // Test pause and resume
        activityRule.scenario.onActivity { activity ->
            activity.onPause()
        }
        
        // Wait a bit
        Thread.sleep(500)
        
        activityRule.scenario.onActivity { activity ->
            activity.onResume()
        }
        
        // Activity should still be running
        activityRule.scenario.onActivity { activity ->
            assert(activity.isDestroyed.not())
        }
    }
}
